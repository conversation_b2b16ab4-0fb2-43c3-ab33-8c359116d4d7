{"error_type": "ValueError", "error_message": "Failed to create Pydantic model from parsed JSON: 5 validation errors for SocialMediaAnalysisSignal\npublic_sentiment_analysis\n  Field required [type=missing, input_value={'signal': 'bearish', 'co...uture market behavior.'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\ninsider_activity_analysis\n  Field required [type=missing, input_value={'signal': 'bearish', 'co...uture market behavior.'}, input_type=dict]\n    For further information visit https:/", "model_name": "glm-4-flash", "model_provider": "QingYun", "agent_name": "social_media_analyst_agent", "attempt_number": 1, "max_retries": 3, "timestamp": "2025-07-07T20:51:39.318798"}