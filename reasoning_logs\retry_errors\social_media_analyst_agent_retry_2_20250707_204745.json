{"error_type": "ValueError", "error_message": "Failed to create Pydantic model from parsed JSON: 1 validation error for SocialMediaAnalysisSignal\nreasoning\n  Input should be a valid string [type=string_type, input_value={'historical_social_media...ture market movements.\"}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/string_type", "model_name": "glm-4-flash", "model_provider": "QingYun", "agent_name": "social_media_analyst_agent", "attempt_number": 2, "max_retries": 3, "timestamp": "2025-07-07T20:47:45.706543"}