{"error_type": "ValueError", "error_message": "Failed to create Pydantic model from parsed JSON: 5 validation errors for NewsAnalysisSignal\nsentiment_analysis\n  Field required [type=missing, input_value={'signal': 'neutral', 'co...e level of confidence.'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing\nevent_impact_analysis\n  Field required [type=missing, input_value={'signal': 'neutral', 'co...e level of confidence.'}, input_type=dict]\n    For further information visit https://errors.pydantic.d", "model_name": "glm-4-flash", "model_provider": "QingYun", "agent_name": "news_analyst_agent", "attempt_number": 1, "max_retries": 3, "timestamp": "2025-07-07T20:51:44.441125"}